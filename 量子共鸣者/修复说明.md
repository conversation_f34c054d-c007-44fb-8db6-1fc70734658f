# 量子共鸣者关卡选择屏幕切换问题修复

## 问题描述
量子共鸣者选择关卡后开启游戏时，控制台提示：
```
📱 屏幕切换完成: levelSelectScreen → main-menu-screen
```

这表明游戏没有正确切换到游戏屏幕，而是错误地返回到了主菜单屏幕。

## 问题根因分析

### 原始代码流程问题
在 `量子共鸣者/js/ui/level-select.js` 文件中，`startLevel()` 方法的执行流程存在时序问题：

1. **第555行**：调用 `this.hide()` 隐藏关卡选择界面
2. **第618行**：`hide()` 方法中立即调用 `uiManager.showScreen('main-menu-screen')` 切换到主菜单
3. **第559行**：然后调用 `gameController.startLevel()` 启动游戏
4. **游戏控制器**：`startLevel()` 方法调用 `showGameScreen()` 尝试切换到游戏屏幕

### 时序冲突
- 关卡选择界面的 `hide()` 方法会立即触发屏幕切换到主菜单
- 游戏控制器随后尝试切换到游戏屏幕
- 由于屏幕切换的异步性质，最终显示的是主菜单屏幕而不是游戏屏幕

## 修复方案

### 1. 修改关卡选择的启动流程
修改 `startLevel()` 方法，改变执行顺序：
- 先隐藏关卡选择界面（但不返回主菜单）
- 然后让游戏控制器处理屏幕切换

### 2. 新增专用隐藏方法
添加 `hideWithoutReturnToMenu()` 方法：
- 只负责隐藏关卡选择界面
- 不自动返回主菜单
- 让游戏控制器完全控制屏幕切换

### 3. 增强调试日志
在关键位置添加详细的调试日志，便于追踪屏幕切换流程。

## 修复后的代码流程

### 新的 startLevel() 方法
```javascript
startLevel() {
    if (!this.selectedLevel) {
        console.warn('⚠️ 没有选中的关卡');
        return;
    }

    console.log(`🎮 关卡选择：开始关卡 ${this.selectedLevel.name} (${this.selectedDifficulty})`);

    if (window.gameController) {
        console.log('🎮 关卡选择：调用游戏控制器启动关卡');
        
        // 先隐藏关卡选择界面（不返回主菜单）
        this.hideWithoutReturnToMenu();
        
        // 延迟启动游戏，确保界面隐藏动画开始
        setTimeout(() => {
            console.log('🎮 关卡选择：开始调用 gameController.startLevel()');
            gameController.startLevel(this.selectedLevel.id, this.selectedDifficulty);
        }, 100);
    } else {
        console.error('❌ 游戏控制器未初始化');
        this.hide(); // 备用方案：返回主菜单
    }
}
```

### 新增的 hideWithoutReturnToMenu() 方法
```javascript
hideWithoutReturnToMenu() {
    if (this.elements.container) {
        this.elements.container.classList.add('screen-exit');
        
        setTimeout(() => {
            this.elements.container.style.display = 'none';
            this.elements.container.classList.remove('screen-exit');
        }, 500);
    }
    
    this.isVisible = false;
    
    console.log('🎮 关卡选择界面已隐藏，等待游戏控制器处理屏幕切换');
}
```

## 预期效果

修复后，控制台应该显示类似以下的日志序列：
```
🎮 关卡选择：开始关卡 [关卡名] ([难度])
🎮 关卡选择：调用游戏控制器启动关卡
🎮 关卡选择界面已隐藏，等待游戏控制器处理屏幕切换
🎮 关卡选择：开始调用 gameController.startLevel()
🎮 开始关卡: [关卡ID] ([难度])
🎮 游戏控制器：开始显示游戏屏幕
🎮 游戏控制器：调用 uiManager.showScreen("game-screen")
📱 屏幕切换完成: levelSelectScreen → game-screen
✅ 游戏屏幕显示正常
✅ 关卡 [关卡ID] 启动成功
```

## 测试建议

1. 选择任意关卡并点击开始游戏
2. 观察控制台日志，确认屏幕切换流程正确
3. 验证游戏界面正常显示，而不是返回主菜单
4. 测试不同难度的关卡，确保修复对所有情况都有效

## 相关文件

- `量子共鸣者/js/ui/level-select.js` - 关卡选择界面逻辑
- `量子共鸣者/js/game/game-controller.js` - 游戏控制器
- `量子共鸣者/js/ui/ui-manager.js` - UI管理器
