/**
 * 量子共鸣者 - 关卡选择组件
 * 负责管理关卡选择界面，包括关卡预览、难度选择、进度显示
 */

class LevelSelect {
    constructor() {
        this.isInitialized = false;
        this.isVisible = false;
        
        // 关卡选择元素
        this.elements = {
            container: null,
            levelGrid: null,
            levelPreview: null,
            difficultySelector: null,
            startButton: null,
            backButton: null
        };
        
        // 当前选中的关卡
        this.selectedLevel = null;
        this.selectedDifficulty = 'normal';
        
        // 关卡数据
        this.levels = [
            {
                id: 'tutorial',
                name: '量子入门',
                description: '学习基础的量子共鸣操作',
                difficulty: ['easy'],
                unlocked: true,
                bestScore: 0,
                stars: 0,
                preview: {
                    particles: 8,
                    connections: 6,
                    theme: 'quantum'
                }
            },
            {
                id: 'resonance_basics',
                name: '共鸣基础',
                description: '掌握粒子共鸣的基本原理',
                difficulty: ['easy', 'normal'],
                unlocked: true,
                bestScore: 0,
                stars: 0,
                preview: {
                    particles: 12,
                    connections: 10,
                    theme: 'quantum'
                }
            },
            {
                id: 'chain_reaction',
                name: '连锁反应',
                description: '创造壮观的连锁反应效果',
                difficulty: ['normal', 'hard'],
                unlocked: false,
                bestScore: 0,
                stars: 0,
                preview: {
                    particles: 16,
                    connections: 15,
                    theme: 'energy'
                }
            },
            {
                id: 'frequency_master',
                name: '频率大师',
                description: '精确控制多种频率的共鸣',
                difficulty: ['normal', 'hard', 'expert'],
                unlocked: false,
                bestScore: 0,
                stars: 0,
                preview: {
                    particles: 20,
                    connections: 18,
                    theme: 'space'
                }
            },
            {
                id: 'quantum_symphony',
                name: '量子交响曲',
                description: '创造完美的量子音乐体验',
                difficulty: ['hard', 'expert'],
                unlocked: false,
                bestScore: 0,
                stars: 0,
                preview: {
                    particles: 24,
                    connections: 22,
                    theme: 'quantum'
                }
            },
            {
                id: 'dimensional_rift',
                name: '维度裂隙',
                description: '在多维空间中操控量子场',
                difficulty: ['expert'],
                unlocked: false,
                bestScore: 0,
                stars: 0,
                preview: {
                    particles: 30,
                    connections: 28,
                    theme: 'space'
                }
            }
        ];
        
        console.log('🎯 关卡选择组件已创建');
    }

    /**
     * 初始化关卡选择界面
     */
    init() {
        try {
            // 获取关卡选择元素
            this.getLevelSelectElements();
            
            // 创建关卡选择结构
            this.createLevelSelectStructure();
            
            // 加载关卡进度
            this.loadLevelProgress();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 渲染关卡网格
            this.renderLevelGrid();
            
            this.isInitialized = true;
            console.log('✅ 关卡选择界面初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 关卡选择界面初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取关卡选择元素
     */
    getLevelSelectElements() {
        this.elements.container = document.getElementById('levelSelectScreen') || 
                                 document.querySelector('.level-select-screen');
    }

    /**
     * 创建关卡选择结构
     */
    createLevelSelectStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ 关卡选择容器不存在，跳过结构创建');
            return;
        }

        this.elements.container.innerHTML = `
            <div class="level-select-header">
                <h2>选择关卡</h2>
                <button class="level-back-button" id="levelBackButton">返回</button>
            </div>
            
            <div class="level-select-content">
                <div class="level-grid" id="levelGrid">
                    <!-- 关卡卡片将在这里动态生成 -->
                </div>
                
                <div class="level-preview" id="levelPreview">
                    <div class="preview-header">
                        <h3 id="previewTitle">选择一个关卡</h3>
                        <div class="preview-stars" id="previewStars">
                            <span class="star">☆</span>
                            <span class="star">☆</span>
                            <span class="star">☆</span>
                        </div>
                    </div>
                    
                    <div class="preview-description" id="previewDescription">
                        点击左侧的关卡卡片查看详细信息
                    </div>
                    
                    <div class="preview-canvas-container">
                        <canvas id="levelPreviewCanvas" width="300" height="200"></canvas>
                    </div>
                    
                    <div class="preview-stats" id="previewStats">
                        <div class="stat-item">
                            <span class="stat-label">最佳分数</span>
                            <span class="stat-value" id="previewBestScore">--</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">粒子数量</span>
                            <span class="stat-value" id="previewParticles">--</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">连接数量</span>
                            <span class="stat-value" id="previewConnections">--</span>
                        </div>
                    </div>
                    
                    <div class="difficulty-selector" id="difficultySelector">
                        <h4>选择难度</h4>
                        <div class="difficulty-buttons" id="difficultyButtons">
                            <!-- 难度按钮将在这里动态生成 -->
                        </div>
                    </div>
                    
                    <div class="preview-actions">
                        <button class="quantum-button primary" id="startLevelButton" disabled>
                            开始游戏
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 更新元素引用
        this.elements.levelGrid = document.getElementById('levelGrid');
        this.elements.levelPreview = document.getElementById('levelPreview');
        this.elements.difficultySelector = document.getElementById('difficultySelector');
        this.elements.startButton = document.getElementById('startLevelButton');
        this.elements.backButton = document.getElementById('levelBackButton');

        // 验证元素是否正确创建
        const elementsToCheck = [
            { name: 'levelGrid', element: this.elements.levelGrid },
            { name: 'levelPreview', element: this.elements.levelPreview },
            { name: 'startButton', element: this.elements.startButton },
            { name: 'backButton', element: this.elements.backButton }
        ];

        elementsToCheck.forEach(({ name, element }) => {
            if (element) {
                console.log(`✅ ${name} 元素创建成功`);
            } else {
                console.error(`❌ ${name} 元素创建失败`);
            }
        });
    }

    /**
     * 加载关卡进度
     */
    loadLevelProgress() {
        if (window.storageService) {
            const progress = storageService.getLevelProgress();
            if (progress) {
                this.levels.forEach(level => {
                    const levelProgress = progress[level.id];
                    if (levelProgress) {
                        level.unlocked = levelProgress.unlocked || level.unlocked;
                        level.bestScore = levelProgress.bestScore || 0;
                        level.stars = levelProgress.stars || 0;
                    }
                });
            }
        }
    }

    /**
     * 渲染关卡网格
     */
    renderLevelGrid() {
        if (!this.elements.levelGrid) return;

        this.elements.levelGrid.innerHTML = '';

        this.levels.forEach((level, index) => {
            const levelCard = this.createLevelCard(level, index);
            this.elements.levelGrid.appendChild(levelCard);
        });
    }

    /**
     * 创建关卡卡片
     * @param {Object} level - 关卡数据
     * @param {number} index - 关卡索引
     * @returns {HTMLElement} 关卡卡片元素
     */
    createLevelCard(level, index) {
        const card = document.createElement('div');
        card.className = `level-card ${level.unlocked ? 'unlocked' : 'locked'}`;
        card.dataset.levelId = level.id;
        
        // 星星显示
        const starsHtml = Array.from({length: 3}, (_, i) => 
            `<span class="star ${i < level.stars ? 'filled' : ''}">${i < level.stars ? '★' : '☆'}</span>`
        ).join('');

        card.innerHTML = `
            <div class="level-card-header">
                <div class="level-number">${index + 1}</div>
                <div class="level-stars">${starsHtml}</div>
            </div>
            
            <div class="level-card-content">
                <h3 class="level-name">${level.name}</h3>
                <p class="level-description">${level.description}</p>
                
                <div class="level-info">
                    <div class="level-best-score">
                        最佳: ${level.bestScore > 0 ? level.bestScore.toLocaleString() : '--'}
                    </div>
                    <div class="level-difficulty-badges">
                        ${level.difficulty.map(diff => 
                            `<span class="difficulty-badge ${diff}">${this.getDifficultyName(diff)}</span>`
                        ).join('')}
                    </div>
                </div>
            </div>
            
            ${!level.unlocked ? '<div class="level-lock-overlay"><span class="lock-icon">🔒</span></div>' : ''}
        `;

        // 添加点击事件
        if (level.unlocked) {
            card.addEventListener('click', () => this.selectLevel(level));
        }

        return card;
    }

    /**
     * 获取难度名称
     * @param {string} difficulty - 难度标识
     * @returns {string} 难度名称
     */
    getDifficultyName(difficulty) {
        const names = {
            'easy': '简单',
            'normal': '普通',
            'hard': '困难',
            'expert': '专家'
        };
        return names[difficulty] || difficulty;
    }

    /**
     * 选择关卡
     * @param {Object} level - 关卡数据
     */
    selectLevel(level) {
        this.selectedLevel = level;
        
        // 更新卡片选中状态
        document.querySelectorAll('.level-card').forEach(card => {
            card.classList.toggle('selected', card.dataset.levelId === level.id);
        });
        
        // 更新预览
        this.updateLevelPreview(level);
        
        // 更新难度选择器
        this.updateDifficultySelector(level);
        
        // 启用开始按钮
        if (this.elements.startButton) {
            this.elements.startButton.disabled = false;
        }
    }

    /**
     * 更新关卡预览
     * @param {Object} level - 关卡数据
     */
    updateLevelPreview(level) {
        // 更新标题和描述
        const titleElement = document.getElementById('previewTitle');
        const descriptionElement = document.getElementById('previewDescription');
        const starsElement = document.getElementById('previewStars');
        
        if (titleElement) titleElement.textContent = level.name;
        if (descriptionElement) descriptionElement.textContent = level.description;
        
        // 更新星星显示
        if (starsElement) {
            const stars = starsElement.querySelectorAll('.star');
            stars.forEach((star, index) => {
                star.textContent = index < level.stars ? '★' : '☆';
                star.classList.toggle('filled', index < level.stars);
            });
        }
        
        // 更新统计信息
        const bestScoreElement = document.getElementById('previewBestScore');
        const particlesElement = document.getElementById('previewParticles');
        const connectionsElement = document.getElementById('previewConnections');
        
        if (bestScoreElement) {
            bestScoreElement.textContent = level.bestScore > 0 ? 
                level.bestScore.toLocaleString() : '--';
        }
        if (particlesElement) {
            particlesElement.textContent = level.preview.particles;
        }
        if (connectionsElement) {
            connectionsElement.textContent = level.preview.connections;
        }
        
        // 渲染预览画布
        this.renderPreviewCanvas(level);
    }

    /**
     * 渲染预览画布
     * @param {Object} level - 关卡数据
     */
    renderPreviewCanvas(level) {
        const canvas = document.getElementById('levelPreviewCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 设置背景
        const gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, width/2);
        gradient.addColorStop(0, 'rgba(26, 26, 46, 0.8)');
        gradient.addColorStop(1, 'rgba(13, 20, 33, 0.9)');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        
        // 生成预览粒子
        const particles = [];
        for (let i = 0; i < level.preview.particles; i++) {
            particles.push({
                x: Math.random() * (width - 40) + 20,
                y: Math.random() * (height - 40) + 20,
                size: 3 + Math.random() * 4,
                hue: this.getThemeHue(level.preview.theme) + Math.random() * 60 - 30
            });
        }
        
        // 绘制连接线
        ctx.strokeStyle = 'rgba(0, 255, 255, 0.3)';
        ctx.lineWidth = 1;
        for (let i = 0; i < level.preview.connections && i < particles.length - 1; i++) {
            const p1 = particles[i];
            const p2 = particles[i + 1];
            
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.stroke();
        }
        
        // 绘制粒子
        particles.forEach(particle => {
            ctx.save();
            
            // 发光效果
            ctx.shadowColor = `hsl(${particle.hue}, 100%, 50%)`;
            ctx.shadowBlur = 10;
            
            // 粒子本体
            ctx.fillStyle = `hsl(${particle.hue}, 100%, 60%)`;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        });
    }

    /**
     * 获取主题色调
     * @param {string} theme - 主题名称
     * @returns {number} 色调值
     */
    getThemeHue(theme) {
        const themes = {
            'quantum': 180,  // 青色
            'energy': 60,    // 黄色
            'space': 240     // 蓝色
        };
        return themes[theme] || 180;
    }

    /**
     * 更新难度选择器
     * @param {Object} level - 关卡数据
     */
    updateDifficultySelector(level) {
        const buttonsContainer = document.getElementById('difficultyButtons');
        if (!buttonsContainer) return;
        
        buttonsContainer.innerHTML = '';
        
        level.difficulty.forEach(difficulty => {
            const button = document.createElement('button');
            button.className = `difficulty-button ${difficulty}`;
            button.textContent = this.getDifficultyName(difficulty);
            button.dataset.difficulty = difficulty;
            
            // 设置默认选中
            if (difficulty === this.selectedDifficulty || 
                (level.difficulty.includes(this.selectedDifficulty) === false && difficulty === level.difficulty[0])) {
                button.classList.add('selected');
                this.selectedDifficulty = difficulty;
            }
            
            button.addEventListener('click', () => this.selectDifficulty(difficulty));
            buttonsContainer.appendChild(button);
        });
    }

    /**
     * 选择难度
     * @param {string} difficulty - 难度标识
     */
    selectDifficulty(difficulty) {
        this.selectedDifficulty = difficulty;
        
        // 更新按钮状态
        document.querySelectorAll('.difficulty-button').forEach(button => {
            button.classList.toggle('selected', button.dataset.difficulty === difficulty);
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 返回按钮
        if (this.elements.backButton) {
            this.elements.backButton.addEventListener('click', () => this.hide());
        }
        
        // 开始游戏按钮
        if (this.elements.startButton) {
            this.elements.startButton.addEventListener('click', () => this.startLevel());
        }
    }

    /**
     * 开始关卡
     */
    startLevel() {
        if (!this.selectedLevel) {
            console.warn('⚠️ 没有选中的关卡');
            return;
        }

        console.log(`🎮 关卡选择：开始关卡 ${this.selectedLevel.name} (${this.selectedDifficulty})`);

        // 先启动游戏，让游戏控制器处理屏幕切换
        if (window.gameController) {
            console.log('🎮 关卡选择：调用游戏控制器启动关卡');

            // 先隐藏关卡选择界面（不返回主菜单）
            this.hideWithoutReturnToMenu();

            // 延迟启动游戏，确保界面隐藏动画开始
            setTimeout(() => {
                console.log('🎮 关卡选择：开始调用 gameController.startLevel()');
                gameController.startLevel(this.selectedLevel.id, this.selectedDifficulty);
            }, 100);
        } else {
            console.error('❌ 游戏控制器未初始化');
            // 如果游戏控制器不存在，则返回主菜单
            this.hide();
        }
    }

    /**
     * 显示关卡选择界面
     */
    show() {
        if (!this.isInitialized) {
            const initResult = this.init();
            if (!initResult) {
                console.error('❌ 关卡选择界面初始化失败');
                return;
            }
        }

        // 刷新关卡进度
        this.loadLevelProgress();
        this.renderLevelGrid();

        // 显示界面
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.style.opacity = '1';
            this.elements.container.style.visibility = 'visible';
            this.elements.container.classList.add('active');
            this.elements.container.classList.add('screen-enter');

            setTimeout(() => {
                this.elements.container.classList.remove('screen-enter');
            }, 500);

            console.log('✅ 关卡选择界面已显示');
        } else {
            console.error('❌ 关卡选择容器不存在');
        }

        this.isVisible = true;
    }

    /**
     * 隐藏关卡选择界面
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('screen-exit');

            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('screen-exit');
            }, 500);
        }

        this.isVisible = false;

        // 返回主菜单
        if (window.uiManager) {
            uiManager.showScreen('main-menu-screen');
        }
    }

    /**
     * 隐藏关卡选择界面但不返回主菜单
     * 用于开始游戏时，让游戏控制器处理屏幕切换
     */
    hideWithoutReturnToMenu() {
        if (this.elements.container) {
            this.elements.container.classList.add('screen-exit');

            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('screen-exit');
            }, 500);
        }

        this.isVisible = false;

        console.log('🎮 关卡选择界面已隐藏，等待游戏控制器处理屏幕切换');
    }

    /**
     * 解锁关卡
     * @param {string} levelId - 关卡ID
     */
    unlockLevel(levelId) {
        const level = this.levels.find(l => l.id === levelId);
        if (level) {
            level.unlocked = true;
            
            // 保存进度
            if (window.storageService) {
                storageService.updateLevelProgress(levelId, { unlocked: true });
            }
            
            // 重新渲染
            if (this.isVisible) {
                this.renderLevelGrid();
            }
        }
    }

    /**
     * 更新关卡分数
     * @param {string} levelId - 关卡ID
     * @param {number} score - 分数
     * @param {number} stars - 星级
     */
    updateLevelScore(levelId, score, stars) {
        const level = this.levels.find(l => l.id === levelId);
        if (level) {
            level.bestScore = Math.max(level.bestScore, score);
            level.stars = Math.max(level.stars, stars);
            
            // 保存进度
            if (window.storageService) {
                storageService.updateLevelProgress(levelId, {
                    bestScore: level.bestScore,
                    stars: level.stars
                });
            }
            
            // 重新渲染
            if (this.isVisible) {
                this.renderLevelGrid();
                if (this.selectedLevel && this.selectedLevel.id === levelId) {
                    this.updateLevelPreview(level);
                }
            }
        }
    }

    /**
     * 销毁关卡选择界面
     */
    destroy() {
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.selectedLevel = null;
        this.isInitialized = false;
        console.log('🎯 关卡选择组件已销毁');
    }
}

// 创建全局关卡选择实例
window.levelSelect = new LevelSelect();
